package project

import (
	"database/sql"
	"log/slog"
	"os"
	"testing"
	"time"

	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/logger"
	"github.com/koopa0/pms-api-v2/internal/testutils"
	"github.com/koopa0/pms-api-v2/internal/types"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// Following Architecture.md principle: test code defines interfaces based on what it needs
// Project package provides concrete implementations, tests verify behavior

func TestNewService(t *testing.T) {
	t.<PERSON>llel()

	queries := &sqlc.Queries{}
	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}

	service := NewService(queries, logger)

	if service == nil {
		t.Error("expected service to be created")
	}
	if service.queries != queries {
		t.Error("expected queries to be set")
	}
	if service.logger != logger {
		t.Error("expected logger to be set")
	}
}

func TestService_CreateProject(t *testing.T) {
	t.<PERSON>()

	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	service := NewService(&sqlc.Queries{}, logger)

	tests := []struct {
		name        string
		request     CreateProjectRequest
		creatorID   int32
		expectError bool
	}{
		{
			name: "valid project creation",
			request: CreateProjectRequest{
				Name:          "Test Project",
				Type:          constants.ProjectTypes.General,
				Category:      constants.ProjectCategories.CloudService,
				AgencyName:    "Test Agency",
				ContactPerson: "John Doe",
				ContactEmail:  "<EMAIL>",
				QuoteDeadline: time.Now().AddDate(0, 0, 30),
			},
			creatorID:   1,
			expectError: false,
		},
		{
			name: "project with optional fields",
			request: CreateProjectRequest{
				Name:                 "Advanced Project",
				Type:                 constants.ProjectTypes.Periodic,
				Category:             constants.ProjectCategories.InfoService,
				Description:          stringPtr("Detailed project description"),
				AgencyName:           "Advanced Agency",
				ContactPerson:        "Jane Smith",
				ContactEmail:         "<EMAIL>",
				ContactPhone:         stringPtr("+886-2-1234-5678"),
				QuoteDeadline:        time.Now().AddDate(0, 0, 45),
				RequiredDeliveryDate: timePtr(time.Now().AddDate(0, 3, 0)),
				Budget:               float64Ptr(1000000.0),
				Requirements:         stringPtr("Specific technical requirements"),
			},
			creatorID:   2,
			expectError: false,
		},
		{
			name: "project with minimal required fields",
			request: CreateProjectRequest{
				Name:          "Minimal Project",
				Type:          constants.ProjectTypes.General,
				Category:      constants.ProjectCategories.CloudService,
				AgencyName:    "Minimal Agency",
				ContactPerson: "Min User",
				ContactEmail:  "<EMAIL>",
				QuoteDeadline: time.Now().AddDate(0, 0, 15),
			},
			creatorID:   3,
			expectError: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := testutils.CreateTestContext()
			project, err := service.CreateProject(ctx, tt.request, tt.creatorID)

			if tt.expectError {
				if err == nil {
					t.Error("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if project == nil {
				t.Error("expected project but got nil")
				return
			}

			// Verify required fields
			if project.Name != tt.request.Name {
				t.Errorf("expected name %s, got %s", tt.request.Name, project.Name)
			}
			if project.Type != tt.request.Type {
				t.Errorf("expected type %s, got %s", tt.request.Type, project.Type)
			}
			if project.Category != tt.request.Category {
				t.Errorf("expected category %s, got %s", tt.request.Category, project.Category)
			}
			if project.Status != constants.ProjectStatuses.Active {
				t.Errorf("expected status %s, got %s", constants.ProjectStatuses.Active, project.Status)
			}

			// Verify ID is set
			if project.ID <= 0 {
				t.Error("expected project ID to be set")
			}

			// Verify timestamps are set
			if project.CreatedAt.IsZero() {
				t.Error("expected CreatedAt to be set")
			}
			if project.UpdatedAt.IsZero() {
				t.Error("expected UpdatedAt to be set")
			}
		})
	}
}

func TestService_GetProjectByID(t *testing.T) {
	t.Parallel()

	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	service := NewService(&sqlc.Queries{}, logger)

	tests := []struct {
		name        string
		projectID   int32
		expectError bool
	}{
		{
			name:        "valid project ID",
			projectID:   1,
			expectError: false,
		},
		{
			name:        "another valid project ID",
			projectID:   999,
			expectError: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := testutils.CreateTestContext()
			project, err := service.GetProjectByID(ctx, tt.projectID)

			if tt.expectError {
				if err == nil {
					t.Error("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if project == nil {
				t.Error("expected project but got nil")
				return
			}

			if project.ID != tt.projectID {
				t.Errorf("expected project ID %d, got %d", tt.projectID, project.ID)
			}

			// Verify required fields are set
			if project.Name == "" {
				t.Error("expected Name to be set")
			}
			if project.AgencyName == "" {
				t.Error("expected AgencyName to be set")
			}
			if project.ContactPerson == "" {
				t.Error("expected ContactPerson to be set")
			}
			if project.ContactEmail == "" {
				t.Error("expected ContactEmail to be set")
			}
			if project.QuoteDeadline.IsZero() {
				t.Error("expected QuoteDeadline to be set")
			}
		})
	}
}

func TestService_ListProjects(t *testing.T) {
	t.Parallel()

	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	service := NewService(&sqlc.Queries{}, logger)

	tests := []struct {
		name        string
		request     ListProjectsRequest
		expectError bool
	}{
		{
			name: "basic list request",
			request: ListProjectsRequest{
				Pagination: types.PaginationParams{
					Page:     1,
					PageSize: 20,
				},
			},
			expectError: false,
		},
		{
			name: "list with filters",
			request: ListProjectsRequest{
				Status:   string(constants.ProjectStatuses.Active),
				Type:     string(constants.ProjectTypes.General),
				Category: string(constants.ProjectCategories.CloudService),
				Pagination: types.PaginationParams{
					Page:     1,
					PageSize: 10,
				},
			},
			expectError: false,
		},
		{
			name: "list with different pagination",
			request: ListProjectsRequest{
				Status: string(constants.ProjectStatuses.Closed),
				Pagination: types.PaginationParams{
					Page:     2,
					PageSize: 5,
				},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			ctx := testutils.CreateTestContext()
			response, err := service.ListProjects(ctx, tt.request)

			if tt.expectError {
				if err == nil {
					t.Error("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if response == nil {
				t.Error("expected response but got nil")
				return
			}

			// Verify pagination structure
			if response.Data == nil {
				t.Error("expected Data to be set")
			}

			if response.Pagination.Page != tt.request.Pagination.Page {
				t.Errorf("expected page %d, got %d", tt.request.Pagination.Page, response.Pagination.Page)
			}

			if response.Pagination.PageSize != tt.request.Pagination.PageSize {
				t.Errorf("expected page size %d, got %d", tt.request.Pagination.PageSize, response.Pagination.PageSize)
			}

			if !response.Success {
				t.Error("expected Success to be true")
			}

			// Verify that we have at least some mock data
			if len(response.Data) == 0 {
				t.Error("expected at least one project in mock data")
			}

			// Verify project structure
			for _, project := range response.Data {
				if project.ID <= 0 {
					t.Error("expected project ID to be positive")
				}
				if project.Name == "" {
					t.Error("expected project name to be set")
				}
				if project.AgencyName == "" {
					t.Error("expected agency name to be set")
				}
				if project.ContactEmail == "" {
					t.Error("expected contact email to be set")
				}
			}
		})
	}
}

func TestService_MapProjectToProjectInfo(t *testing.T) {
	t.Parallel()

	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	service := NewService(&sqlc.Queries{}, logger)

	tests := []struct {
		name     string
		project  *sqlc.Project
		expected *Info
	}{
		{
			name: "project with remarks",
			project: &sqlc.Project{
				ID:       1,
				Name:     "Test Project",
				Type:     constants.ProjectTypes.General,
				Category: constants.ProjectCategories.CloudService,
				Status:   constants.ProjectStatuses.Active,
				Remarks:  sqlNullString("Test requirements"),
				IsDelete: false,
			},
			expected: &Info{
				ID:            1,
				Name:          "Test Project",
				Type:          constants.ProjectTypes.General,
				Category:      constants.ProjectCategories.CloudService,
				Status:        constants.ProjectStatuses.Active,
				AgencyName:    "Mock Agency",
				ContactPerson: "Mock Contact",
				ContactEmail:  "<EMAIL>",
				Requirements:  stringPtr("Test requirements"),
			},
		},
		{
			name: "project without remarks",
			project: &sqlc.Project{
				ID:       2,
				Name:     "Another Project",
				Type:     constants.ProjectTypes.Periodic,
				Category: constants.ProjectCategories.InfoService,
				Status:   constants.ProjectStatuses.Closed,
				IsDelete: false,
			},
			expected: &Info{
				ID:            2,
				Name:          "Another Project",
				Type:          constants.ProjectTypes.Periodic,
				Category:      constants.ProjectCategories.InfoService,
				Status:        constants.ProjectStatuses.Closed,
				AgencyName:    "Mock Agency",
				ContactPerson: "Mock Contact",
				ContactEmail:  "<EMAIL>",
				Requirements:  nil,
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			result := service.mapProjectToProjectInfo(tt.project)

			if result.ID != tt.expected.ID {
				t.Errorf("expected ID %d, got %d", tt.expected.ID, result.ID)
			}
			if result.Name != tt.expected.Name {
				t.Errorf("expected Name %s, got %s", tt.expected.Name, result.Name)
			}
			if result.Type != tt.expected.Type {
				t.Errorf("expected Type %s, got %s", tt.expected.Type, result.Type)
			}
			if result.Category != tt.expected.Category {
				t.Errorf("expected Category %s, got %s", tt.expected.Category, result.Category)
			}
			if result.Status != tt.expected.Status {
				t.Errorf("expected Status %s, got %s", tt.expected.Status, result.Status)
			}

			// Check optional fields
			if tt.expected.Requirements != nil {
				if result.Requirements == nil || *result.Requirements != *tt.expected.Requirements {
					t.Errorf("expected Requirements %v, got %v", tt.expected.Requirements, result.Requirements)
				}
			} else {
				if result.Requirements != nil {
					t.Errorf("expected Requirements to be nil, got %v", result.Requirements)
				}
			}

			// Verify mock fields are set
			if result.AgencyName != "Mock Agency" {
				t.Errorf("expected AgencyName to be 'Mock Agency', got %s", result.AgencyName)
			}
			if result.ContactEmail != "<EMAIL>" {
				t.Errorf("expected ContactEmail to be '<EMAIL>', got %s", result.ContactEmail)
			}
		})
	}
}

func TestServiceErrors(t *testing.T) {
	t.Parallel()

	// Test that error constants are defined
	if ErrProjectNotFound == nil {
		t.Error("expected ErrProjectNotFound to be defined")
	}
	if ErrInvalidInput == nil {
		t.Error("expected ErrInvalidInput to be defined")
	}
	if ErrInternalError == nil {
		t.Error("expected ErrInternalError to be defined")
	}

	// Test error messages
	if ErrProjectNotFound.Error() == "" {
		t.Error("expected ErrProjectNotFound to have a message")
	}
	if ErrInvalidInput.Error() == "" {
		t.Error("expected ErrInvalidInput to have a message")
	}
	if ErrInternalError.Error() == "" {
		t.Error("expected ErrInternalError to have a message")
	}
}

// Helper functions for creating pointers
func stringPtr(s string) *string {
	return &s
}

func timePtr(t time.Time) *time.Time {
	return &t
}

func float64Ptr(f float64) *float64 {
	return &f
}

func sqlNullString(s string) sql.NullString {
	return sql.NullString{String: s, Valid: true}
}
