package auth

import (
	"bytes"
	"context"
	"encoding/json"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/koopa0/pms-api-v2/internal/api"
	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/logger"
	"github.com/koopa0/pms-api-v2/internal/testutils"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// MockAuthService implements the auth service interface for testing
type MockAuthService struct {
	loginResult     *LoginResponse
	loginError      error
	registerResult  *RegistrationResponse
	registerError   error
	getUserResult   *UserInfo
	getUserError    error
	shouldFailLogin bool
}

func NewMockAuthService() *MockAuthService {
	return &MockAuthService{
		loginResult: &LoginResponse{
			User: &UserInfo{
				ID:                1,
				Username:          "admin",
				Email:             "<EMAIL>",
				Role:              sqlc.UserRoleSPO,
				Status:            sqlc.UserStatusValue0,
				LastLoginAt:       time.Now(),
				PasswordExpiredAt: time.Now().Add(24 * time.Hour),
			},
			Token: "mock-jwt-token",
		},
		registerResult: &RegistrationResponse{
			ID:      1,
			Status:  constants.RegistrationStatuses.PendingRegistration,
			Message: constants.MsgRegistered,
		},
		getUserResult: &UserInfo{
			ID:                1,
			Username:          "admin",
			Email:             "<EMAIL>",
			Role:              sqlc.UserRoleSPO,
			Status:            sqlc.UserStatusValue0,
			LastLoginAt:       time.Now(),
			PasswordExpiredAt: time.Now().Add(24 * time.Hour),
		},
	}
}

func (m *MockAuthService) Login(ctx context.Context, req LoginRequest) (*LoginResponse, error) {
	if m.shouldFailLogin || m.loginError != nil {
		return nil, m.loginError
	}
	return m.loginResult, nil
}

func (m *MockAuthService) Register(ctx context.Context, req RegistrationRequest) (*RegistrationResponse, error) {
	if m.registerError != nil {
		return nil, m.registerError
	}
	return m.registerResult, nil
}

func (m *MockAuthService) GetUserByID(ctx context.Context, userID int32) (*UserInfo, error) {
	if m.getUserError != nil {
		return nil, m.getUserError
	}
	return m.getUserResult, nil
}

func TestHandler_Login(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		requestBody    interface{}
		setupMock      func(*MockAuthService)
		expectedStatus int
		expectedResult func(*httptest.ResponseRecorder) bool
	}{
		{
			name: "successful login",
			requestBody: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mas *MockAuthService) {
				// Default mock setup is sufficient for success case
			},
			expectedStatus: http.StatusOK,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}

				data, ok := response["data"].(map[string]interface{})
				if !ok {
					return false
				}

				token, hasToken := data["token"].(string)
				user, hasUser := data["user"].(map[string]interface{})

				return hasToken && token == "mock-jwt-token" &&
					hasUser && user["email"] == "<EMAIL>"
			},
		},
		{
			name: "invalid email format",
			requestBody: LoginRequest{
				Email:    "invalid-email",
				Password: "AdminPass123!",
			},
			setupMock:      func(mas *MockAuthService) {},
			expectedStatus: http.StatusBadRequest,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}

				errors, ok := response["errors"].(map[string]interface{})
				if !ok {
					return false
				}

				_, hasEmailError := errors["email"]
				return hasEmailError
			},
		},
		{
			name: "missing required fields",
			requestBody: LoginRequest{
				Email:    "",
				Password: "",
			},
			setupMock:      func(mas *MockAuthService) {},
			expectedStatus: http.StatusBadRequest,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}

				errors, ok := response["errors"].(map[string]interface{})
				if !ok {
					return false
				}

				_, hasEmailError := errors["email"]
				_, hasPasswordError := errors["password"]
				return hasEmailError && hasPasswordError
			},
		},
		{
			name: "invalid credentials",
			requestBody: LoginRequest{
				Email:    "<EMAIL>",
				Password: "WrongPassword",
			},
			setupMock: func(mas *MockAuthService) {
				mas.loginError = ErrInvalidCredentials
			},
			expectedStatus: http.StatusUnauthorized,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				return err == nil && response["message"] != nil
			},
		},
		{
			name: "password expired",
			requestBody: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mas *MockAuthService) {
				mas.loginError = ErrPasswordExpired
			},
			expectedStatus: http.StatusForbidden,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}

				code, ok := response["code"].(string)
				return ok && code == api.ErrCodePasswordExpired
			},
		},
		{
			name: "account deleted",
			requestBody: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mas *MockAuthService) {
				mas.loginError = ErrAccountDeleted
			},
			expectedStatus: http.StatusForbidden,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}

				code, ok := response["code"].(string)
				return ok && code == api.ErrCodeAccountLocked
			},
		},
		{
			name: "account pending approval",
			requestBody: LoginRequest{
				Email:    "<EMAIL>",
				Password: "AdminPass123!",
			},
			setupMock: func(mas *MockAuthService) {
				mas.loginError = ErrAccountPending
			},
			expectedStatus: http.StatusForbidden,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}

				code, ok := response["code"].(string)
				return ok && code == api.ErrCodeAccountPending
			},
		},
		{
			name:           "malformed JSON",
			requestBody:    "invalid json",
			setupMock:      func(mas *MockAuthService) {},
			expectedStatus: http.StatusBadRequest,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				return err == nil && response["message"] != nil
			},
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockService := NewMockAuthService()
			tt.setupMock(mockService)

			logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
			handler := NewHandler(mockService, logger, false)

			// Create request
			req := testutils.NewHTTPRequest(http.MethodPost, "/api/v1/auth/login").
				WithBody(tt.requestBody).
				Build()
			w := httptest.NewRecorder()

			// Execute
			handler.Login(w, req)

			// Assert
			if w.Code != tt.expectedStatus {
				t.Errorf("expected status %d, got %d. Response: %s", tt.expectedStatus, w.Code, w.Body.String())
				return
			}

			if tt.expectedResult != nil && !tt.expectedResult(w) {
				t.Errorf("result validation failed for test case: %s. Response: %s", tt.name, w.Body.String())
			}
		})
	}
}

func TestHandler_Logout(t *testing.T) {
	t.Parallel()

	mockService := NewMockAuthService()
	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	handler := NewHandler(mockService, logger, false)

	req := testutils.NewHTTPRequest(http.MethodPost, "/api/v1/auth/logout").Build()
	w := httptest.NewRecorder()

	handler.Logout(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("expected status %d, got %d", http.StatusOK, w.Code)
	}

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Errorf("failed to unmarshal response: %v", err)
	}

	data, ok := response["data"].(map[string]interface{})
	if !ok {
		t.Errorf("expected data field in response")
		return
	}

	message, ok := data["message"].(string)
	if !ok || message != constants.MsgLoggedOut {
		t.Errorf("expected logout success message, got %v", data)
	}

	// Check that the Set-Cookie header is present to clear the auth cookie
	cookies := w.Header().Get("Set-Cookie")
	if cookies == "" {
		t.Errorf("expected Set-Cookie header to clear auth cookie")
	}
}

func TestHandler_Me(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		setupAuth      func(*http.Request) *http.Request
		setupMock      func(*MockAuthService)
		expectedStatus int
		expectedResult func(*httptest.ResponseRecorder) bool
	}{
		{
			name: "successful user info retrieval",
			setupAuth: func(req *http.Request) *http.Request {
				claims := &Claims{
					UserID:   testutils.CommonTestUsers.Admin.ID,
					Username: testutils.CommonTestUsers.Admin.Username,
					Email:    testutils.CommonTestUsers.Admin.Email,
					Role:     constants.UserRoles.SPO, // Use proper sqlc.UserRole type
				}
				ctx := context.WithValue(req.Context(), ContextKeyUser, claims)
				return req.WithContext(ctx)
			},
			setupMock: func(mas *MockAuthService) {
				// Default mock setup is sufficient
			},
			expectedStatus: http.StatusOK,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}

				data, ok := response["data"].(map[string]interface{})
				if !ok {
					return false
				}

				email, ok := data["email"].(string)
				return ok && email == "<EMAIL>"
			},
		},
		{
			name: "unauthenticated request",
			setupAuth: func(req *http.Request) *http.Request {
				// Return request without authentication context
				return req
			},
			setupMock:      func(mas *MockAuthService) {},
			expectedStatus: http.StatusUnauthorized,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				return err == nil && response["message"] != nil
			},
		},
		{
			name: "user service error",
			setupAuth: func(req *http.Request) *http.Request {
				claims := &Claims{
					UserID:   testutils.CommonTestUsers.Admin.ID,
					Username: testutils.CommonTestUsers.Admin.Username,
					Email:    testutils.CommonTestUsers.Admin.Email,
					Role:     constants.UserRoles.SPO, // Use proper sqlc.UserRole type
				}
				ctx := context.WithValue(req.Context(), ContextKeyUser, claims)
				return req.WithContext(ctx)
			},
			setupMock: func(mas *MockAuthService) {
				mas.getUserError = ErrUserNotFound
			},
			expectedStatus: http.StatusNotFound,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				return err == nil && response["message"] != nil
			},
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockService := NewMockAuthService()
			tt.setupMock(mockService)

			logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
			handler := NewHandler(mockService, logger, false)

			// Create request
			req := testutils.NewHTTPRequest(http.MethodGet, "/api/v1/auth/me").Build()
			req = tt.setupAuth(req)
			w := httptest.NewRecorder()

			// Execute
			handler.Me(w, req)

			// Assert
			if w.Code != tt.expectedStatus {
				t.Errorf("expected status %d, got %d. Response: %s", tt.expectedStatus, w.Code, w.Body.String())
				return
			}

			if tt.expectedResult != nil && !tt.expectedResult(w) {
				t.Errorf("result validation failed for test case: %s. Response: %s", tt.name, w.Body.String())
			}
		})
	}
}

func TestHandler_Register(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		requestBody    interface{}
		setupMock      func(*MockAuthService)
		expectedStatus int
		expectedResult func(*httptest.ResponseRecorder) bool
	}{
		{
			name: "successful registration",
			requestBody: RegistrationRequest{
				UnifiedBusinessNo: "12345676",
				CompanyName:       "Test Company",
				CompanyType:       "軟體廠商",
				CompanyOwner:      "John Doe",
				ContactPerson:     "Jane Doe",
				Email:             "<EMAIL>",
				Password:          "SecurePass123!",
			},
			setupMock: func(mas *MockAuthService) {
				// Default mock setup is sufficient
			},
			expectedStatus: http.StatusOK,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				if err != nil {
					return false
				}

				data, ok := response["data"].(map[string]interface{})
				if !ok {
					return false
				}

				message, hasMessage := data["message"].(string)
				status, hasStatus := data["status"].(string)

				return hasMessage && message == constants.MsgRegistered &&
					hasStatus && status == string(constants.RegistrationStatuses.PendingRegistration)
			},
		},
		{
			name: "registration with optional fields",
			requestBody: RegistrationRequest{
				UnifiedBusinessNo: "87654320",
				CompanyName:       "Another Company",
				CompanyType:       "資訊服務廠商",
				Address:           testutils.StringPtr("123 Test Street"),
				CompanyOwner:      "Alice Smith",
				ContactPerson:     "Bob Smith",
				JobTitle:          testutils.StringPtr("Manager"),
				Phone:             testutils.StringPtr("02-12345678"),
				Mobile:            testutils.StringPtr("0912-345678"),
				Email:             "<EMAIL>",
				BackupEmail:       testutils.StringPtr("<EMAIL>"),
				Password:          "AnotherPass123!",
				Remark:            testutils.StringPtr("Test registration"),
			},
			setupMock: func(mas *MockAuthService) {
				// Default mock setup is sufficient
			},
			expectedStatus: http.StatusOK,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				return err == nil && response["data"] != nil
			},
		},
		{
			name: "missing required fields",
			requestBody: RegistrationRequest{
				UnifiedBusinessNo: "",
				CompanyName:       "",
				CompanyType:       "",
				CompanyOwner:      "",
				ContactPerson:     "",
				Email:             "",
				Password:          "",
			},
			setupMock:      func(mas *MockAuthService) {},
			expectedStatus: http.StatusBadRequest,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				return err == nil && response["message"] != nil
			},
		},
		{
			name: "invalid unified business number",
			requestBody: RegistrationRequest{
				UnifiedBusinessNo: "123", // Too short
				CompanyName:       "Test Company",
				CompanyType:       "軟體廠商",
				CompanyOwner:      "John Doe",
				ContactPerson:     "Jane Doe",
				Email:             "<EMAIL>",
				Password:          "SecurePass123!",
			},
			setupMock:      func(mas *MockAuthService) {},
			expectedStatus: http.StatusBadRequest,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				return err == nil && response["message"] != nil
			},
		},
		{
			name: "invalid email format",
			requestBody: RegistrationRequest{
				UnifiedBusinessNo: "12345678",
				CompanyName:       "Test Company",
				CompanyType:       "軟體廠商",
				CompanyOwner:      "John Doe",
				ContactPerson:     "Jane Doe",
				Email:             "invalid-email",
				Password:          "SecurePass123!",
			},
			setupMock:      func(mas *MockAuthService) {},
			expectedStatus: http.StatusBadRequest,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				return err == nil && response["message"] != nil
			},
		},
		{
			name: "weak password",
			requestBody: RegistrationRequest{
				UnifiedBusinessNo: "12345678",
				CompanyName:       "Test Company",
				CompanyType:       "軟體廠商",
				CompanyOwner:      "John Doe",
				ContactPerson:     "Jane Doe",
				Email:             "<EMAIL>",
				Password:          "123", // Too weak
			},
			setupMock:      func(mas *MockAuthService) {},
			expectedStatus: http.StatusBadRequest,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				return err == nil && response["message"] != nil
			},
		},
		{
			name: "service error during registration",
			requestBody: RegistrationRequest{
				UnifiedBusinessNo: "12345676",
				CompanyName:       "Test Company",
				CompanyType:       "軟體廠商",
				CompanyOwner:      "John Doe",
				ContactPerson:     "Jane Doe",
				Email:             "<EMAIL>",
				Password:          "SecurePass123!",
			},
			setupMock: func(mas *MockAuthService) {
				mas.registerError = ErrInternalError
			},
			expectedStatus: http.StatusInternalServerError,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				return err == nil && response["message"] != nil
			},
		},
		{
			name:           "malformed JSON",
			requestBody:    "invalid json",
			setupMock:      func(mas *MockAuthService) {},
			expectedStatus: http.StatusBadRequest,
			expectedResult: func(w *httptest.ResponseRecorder) bool {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				return err == nil && response["message"] != nil
			},
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup
			mockService := NewMockAuthService()
			tt.setupMock(mockService)

			logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
			handler := NewHandler(mockService, logger, false)

			// Create request
			req := testutils.CreateJSONRequest(t, http.MethodPost, "/api/v1/register", tt.requestBody)
			w := httptest.NewRecorder()

			// Execute
			handler.Register(w, req)

			// Assert
			if w.Code != tt.expectedStatus {
				t.Errorf("expected status %d, got %d. Response: %s", tt.expectedStatus, w.Code, w.Body.String())
				return
			}

			if tt.expectedResult != nil && !tt.expectedResult(w) {
				t.Errorf("result validation failed for test case: %s. Response: %s", tt.name, w.Body.String())
			}
		})
	}
}

func TestHandler_validateRegistrationRequest(t *testing.T) {
	t.Parallel()

	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	handler := NewHandler(nil, logger, false)

	tests := []struct {
		name        string
		request     RegistrationRequest
		expectError bool
		errorFields []string
	}{
		{
			name: "valid registration request",
			request: RegistrationRequest{
				UnifiedBusinessNo: "12345676",
				CompanyName:       "Test Company",
				CompanyType:       "軟體廠商",
				CompanyOwner:      "John Doe",
				ContactPerson:     "Jane Doe",
				Email:             "<EMAIL>",
				Password:          "SecurePass123!",
			},
			expectError: false,
		},
		{
			name: "missing required fields",
			request: RegistrationRequest{
				UnifiedBusinessNo: "",
				CompanyName:       "",
				CompanyType:       "",
				CompanyOwner:      "",
				ContactPerson:     "",
				Email:             "",
				Password:          "",
			},
			expectError: true,
			errorFields: []string{"unified_business_no", "company_name", "company_type", "company_owner", "contact_person", "email", "password"},
		},
		{
			name: "invalid unified business number",
			request: RegistrationRequest{
				UnifiedBusinessNo: "123",
				CompanyName:       "Test Company",
				CompanyType:       "軟體廠商",
				CompanyOwner:      "John Doe",
				ContactPerson:     "Jane Doe",
				Email:             "<EMAIL>",
				Password:          "SecurePass123!",
			},
			expectError: true,
			errorFields: []string{"unified_business_no"},
		},
		{
			name: "field length limits exceeded",
			request: RegistrationRequest{
				UnifiedBusinessNo: "12345676",
				CompanyName:       string(make([]byte, 101)), // Exceeds 100 char limit
				CompanyType:       "軟體廠商",
				CompanyOwner:      string(make([]byte, 51)), // Exceeds 50 char limit
				ContactPerson:     "Jane Doe",
				Email:             "<EMAIL>",
				Password:          "SecurePass123!",
			},
			expectError: true,
			errorFields: []string{"company_name", "company_owner"},
		},
		{
			name: "valid with optional fields",
			request: RegistrationRequest{
				UnifiedBusinessNo: "12345676",
				CompanyName:       "Test Company",
				CompanyType:       "軟體廠商",
				Address:           testutils.StringPtr("123 Test Street"),
				CompanyOwner:      "John Doe",
				ContactPerson:     "Jane Doe",
				JobTitle:          testutils.StringPtr("Manager"),
				Phone:             testutils.StringPtr("02-12345678"),
				Mobile:            testutils.StringPtr("0912-345678"),
				Email:             "<EMAIL>",
				BackupEmail:       testutils.StringPtr("<EMAIL>"),
				Password:          "SecurePass123!",
				Remark:            testutils.StringPtr("Test remark"),
			},
			expectError: false,
		},
		{
			name: "optional fields exceed limits",
			request: RegistrationRequest{
				UnifiedBusinessNo: "12345676",
				CompanyName:       "Test Company",
				CompanyType:       "軟體廠商",
				Address:           testutils.StringPtr(string(make([]byte, 201))), // Exceeds 200 char limit
				CompanyOwner:      "John Doe",
				ContactPerson:     "Jane Doe",
				JobTitle:          testutils.StringPtr(string(make([]byte, 51))), // Exceeds 50 char limit
				Email:             "<EMAIL>",
				BackupEmail:       testutils.StringPtr("invalid-email"),
				Password:          "SecurePass123!",
				Remark:            testutils.StringPtr(string(make([]byte, 501))), // Exceeds 500 char limit
			},
			expectError: true,
			errorFields: []string{"address", "job_title", "backup_email", "remark"},
		},
	}

	for _, tt := range tests {
		tt := tt // Capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			err := handler.validateRegistrationRequest(&tt.request)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected validation error, but got nil")
					return
				}

				// If we expect specific error fields, we can add more detailed validation here
				// For now, just check that we got an error
			} else {
				if err != nil {
					t.Errorf("unexpected validation error: %v", err)
				}
			}
		})
	}
}

// Benchmark tests
func BenchmarkHandler_Login(b *testing.B) {
	mockService := NewMockAuthService()
	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	handler := NewHandler(mockService, logger, false)

	reqBody := LoginRequest{
		Email:    "<EMAIL>",
		Password: "AdminPass123!",
	}

	jsonBody, _ := json.Marshal(reqBody)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/auth/login", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.Login(w, req)
	}
}

func BenchmarkHandler_Register(b *testing.B) {
	mockService := NewMockAuthService()
	logger := &logger.Logger{Logger: slog.New(slog.NewTextHandler(os.Stdout, nil))}
	handler := NewHandler(mockService, logger, false)

	reqBody := RegistrationRequest{
		UnifiedBusinessNo: "12345678",
		CompanyName:       "Test Company",
		CompanyType:       "軟體廠商",
		CompanyOwner:      "John Doe",
		ContactPerson:     "Jane Doe",
		Email:             "<EMAIL>",
		Password:          "SecurePass123!",
	}

	jsonBody, _ := json.Marshal(reqBody)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/register", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.Register(w, req)
	}
}
