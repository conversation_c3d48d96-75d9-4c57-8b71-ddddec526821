package auth

import (
	"context"
	"net/http"

	"github.com/koopa0/pms-api-v2/internal/api"
	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/logger"
	"github.com/koopa0/pms-api-v2/internal/validator"
)

// AuthServiceInterface defines the interface for authentication service
type AuthServiceInterface interface {
	Login(ctx context.Context, req LoginRequest) (*LoginResponse, error)
	Register(ctx context.Context, req RegistrationRequest) (*RegistrationResponse, error)
	GetUserByID(ctx context.Context, userID int32) (*UserInfo, error)
}

// Handler handles authentication HTTP requests
type Handler struct {
	authService  AuthServiceInterface
	logger       *logger.Logger
	isProduction bool
}

// NewHandler creates a new authentication handler
func NewHandler(authService AuthServiceInterface, logger *logger.Logger, isProduction bool) *Handler {
	return &Handler{
		authService:  authService,
		logger:       logger,
		isProduction: isProduction,
	}
}

// <PERSON>gin handles user login requests
// POST /api/v1/auth/login
func (h *Handler) Login(w http.ResponseWriter, r *http.Request) {
	var req LoginRequest
	if !api.DecodeJSONRequest(w, r, &req) {
		return
	}

	// Validate request
	v := validator.NewValidator()
	v.Required("email", req.Email)
	v.Email("email", req.Email)
	v.Required("password", req.Password)

	if errs := v.Errors(); errs != nil {
		api.ValidationError(w, "Validation failed", errs.ToMap())
		return
	}

	// Perform login
	loginResp, err := h.authService.Login(r.Context(), req)
	if err != nil {
		h.handleAuthError(w, err)
		return
	}

	// Set auth cookie
	SetAuthCookie(w, loginResp.Token, h.isProduction)

	api.Success(w, loginResp)
}

// Logout handles user logout requests
// POST /api/v1/auth/logout
func (h *Handler) Logout(w http.ResponseWriter, r *http.Request) {
	// Clear auth cookie
	ClearAuthCookie(w, h.isProduction)

	api.Success(w, map[string]string{
		"message": constants.MsgLoggedOut,
	})
}

// Me returns current user information
// GET /api/v1/auth/me
func (h *Handler) Me(w http.ResponseWriter, r *http.Request) {
	userID, _, ok := RequireAuth(w, r)
	if !ok {
		return
	}

	user, err := h.authService.GetUserByID(r.Context(), userID)
	if err != nil {
		h.handleAuthError(w, err)
		return
	}

	api.Success(w, user)
}

// Register handles user registration requests
// POST /api/v1/register
func (h *Handler) Register(w http.ResponseWriter, r *http.Request) {
	var req RegistrationRequest
	if !api.DecodeJSONRequest(w, r, &req) {
		return
	}

	// Validate request
	if err := h.validateRegistrationRequest(&req); err != nil {
		api.BadRequest(w, err.Error())
		return
	}

	// Perform registration
	regResp, err := h.authService.Register(r.Context(), req)
	if err != nil {
		h.handleAuthError(w, err)
		return
	}

	api.Success(w, regResp)
}

// validateRegistrationRequest validates registration request data
func (h *Handler) validateRegistrationRequest(req *RegistrationRequest) error {
	v := validator.NewValidator()

	// Required fields
	v.Required("unified_business_no", req.UnifiedBusinessNo)
	v.UnifiedBusinessNo("unified_business_no", req.UnifiedBusinessNo)

	v.Required("company_name", req.CompanyName)
	v.MaxLength("company_name", req.CompanyName, 100)

	v.Required("company_type", req.CompanyType)
	v.OneOf("company_type", req.CompanyType, []string{"軟體廠商", "資訊服務廠商"})

	v.Required("company_owner", req.CompanyOwner)
	v.MaxLength("company_owner", req.CompanyOwner, 50)

	v.Required("contact_person", req.ContactPerson)
	v.MaxLength("contact_person", req.ContactPerson, 50)

	v.Required("email", req.Email)
	v.Email("email", req.Email)

	v.Required("password", req.Password)
	v.Password("password", req.Password)

	// Optional fields
	if req.Address != nil {
		v.MaxLength("address", *req.Address, 200)
	}
	if req.JobTitle != nil {
		v.MaxLength("job_title", *req.JobTitle, 50)
	}
	if req.Phone != nil {
		v.MaxLength("phone", *req.Phone, 20)
	}
	if req.Mobile != nil {
		v.MaxLength("mobile", *req.Mobile, 20)
	}
	if req.BackupEmail != nil {
		v.Email("backup_email", *req.BackupEmail)
	}
	if req.Remark != nil {
		v.MaxLength("remark", *req.Remark, 500)
	}

	if errs := v.Errors(); errs != nil {
		return errs
	}
	return nil
}

// handleAuthError maps service errors to HTTP responses
func (h *Handler) handleAuthError(w http.ResponseWriter, err error) {
	// Handle specific auth errors differently if needed
	switch err {
	case ErrPasswordExpired:
		api.Error(w, http.StatusForbidden, api.ErrCodePasswordExpired, "Password has expired")
	case ErrAccountDeleted:
		api.Error(w, http.StatusForbidden, api.ErrCodeAccountLocked, "Account has been deleted")
	case ErrAccountPending:
		api.Error(w, http.StatusForbidden, api.ErrCodeAccountPending, "Account pending approval")
	case ErrAccountPendingChanges:
		api.Error(w, http.StatusForbidden, api.ErrCodeAccountPending, "Account changes pending approval")
	case ErrAccountChangesRejected:
		api.Error(w, http.StatusForbidden, api.ErrCodeAccountLocked, "Account changes rejected")
	default:
		// Use common error handler for standard errors
		api.HandleCommonServiceError(w, err, h.logger, "auth service")
	}
}
