package auth

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/koopa0/pms-api-v2/internal/api"
	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/logger"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// Service provides authentication operations
type Service struct {
	queries         *sqlc.Queries
	tokenService    *TokenService
	passwordService *PasswordService
	logger          *logger.Logger
}

// NewService creates a new authentication service
func NewService(queries *sqlc.Queries, tokenService *TokenService, logger *logger.Logger) *Service {
	return &Service{
		queries:         queries,
		tokenService:    tokenService,
		passwordService: NewPasswordService(),
		logger:          logger,
	}
}

// LoginRequest represents login parameters
type LoginRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// RegistrationRequest represents user registration parameters
type RegistrationRequest struct {
	UnifiedBusinessNo string  `json:"unified_business_no" validate:"required,len=8"`
	CompanyName       string  `json:"company_name" validate:"required,max=100"`
	CompanyType       string  `json:"company_type" validate:"required"`
	Address           *string `json:"address" validate:"omitempty,max=200"`
	CompanyOwner      string  `json:"company_owner" validate:"required,max=50"`
	ContactPerson     string  `json:"contact_person" validate:"required,max=50"`
	JobTitle          *string `json:"job_title" validate:"omitempty,max=50"`
	Phone             *string `json:"phone" validate:"omitempty,max=20"`
	Mobile            *string `json:"mobile" validate:"omitempty,max=20"`
	Email             string  `json:"email" validate:"required,email"`
	BackupEmail       *string `json:"backup_email" validate:"omitempty,email"`
	Password          string  `json:"password" validate:"required,min=8"`
	Remark            *string `json:"remark" validate:"omitempty,max=500"`
}

// RegistrationResponse represents successful registration result
type RegistrationResponse struct {
	ID      int32                         `json:"id"`
	Status  sqlc.RegistrationRequestsType `json:"status"`
	Message string                        `json:"message"`
}

// LoginResponse represents successful login result
type LoginResponse struct {
	User  *UserInfo `json:"user"`
	Token string    `json:"token"`
}

// UserInfo represents user information in responses
type UserInfo struct {
	ID                int32           `json:"id"`
	Username          string          `json:"username"`
	Email             string          `json:"email"`
	Role              sqlc.UserRole   `json:"role"`
	Status            sqlc.UserStatus `json:"status"`
	CompanyID         *int32          `json:"company_id,omitempty"`
	CompanyName       *string         `json:"company_name,omitempty"`
	LastLoginAt       time.Time       `json:"last_login_at"`
	PasswordExpiredAt time.Time       `json:"password_expired_at"`
}

// Login authenticates a user and returns login information
func (s *Service) Login(ctx context.Context, req LoginRequest) (*LoginResponse, error) {
	// Get user by email
	user, err := s.queries.GetUserByEmail(ctx, req.Email)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrInvalidCredentials
		}
		s.logger.Error("failed to get user by email", "error", err, "email", req.Email)
		return nil, ErrInternalError
	}

	// Check user status
	if err := s.validateUserStatus(user.Status); err != nil {
		return nil, err
	}

	// Verify password
	if err := s.passwordService.VerifyPassword(req.Password, user.PasswordHash); err != nil {
		return nil, ErrInvalidCredentials
	}

	// Check password expiration
	if user.PasswordExpirationAt.Before(time.Now()) {
		return nil, ErrPasswordExpired
	}

	// Generate access token
	var companyID *int32
	if user.CompanyID.Valid {
		companyID = &user.CompanyID.Int32
	}

	token, err := s.tokenService.GenerateAccessToken(
		user.ID,
		user.Email,
		user.Username,
		user.UserRole,
		companyID,
	)
	if err != nil {
		s.logger.Error("failed to generate access token", "error", err, "user_id", user.ID)
		return nil, ErrInternalError
	}

	// Update last login time
	if err := s.queries.UpdateUserLastLogin(ctx, user.ID); err != nil {
		s.logger.Error("failed to update last login", "error", err, "user_id", user.ID)
		// Continue even if this fails
	}

	// Prepare response
	var companyName *string
	if user.CompanyName.Valid {
		companyName = &user.CompanyName.String
	}

	return &LoginResponse{
		User: &UserInfo{
			ID:                user.ID,
			Username:          user.Username,
			Email:             user.Email,
			Role:              user.UserRole,
			Status:            user.Status,
			CompanyID:         companyID,
			CompanyName:       companyName,
			LastLoginAt:       user.LastLoginAt,
			PasswordExpiredAt: user.PasswordExpirationAt,
		},
		Token: token,
	}, nil
}

// GetUserByID retrieves user information by ID
func (s *Service) GetUserByID(ctx context.Context, userID int32) (*UserInfo, error) {
	user, err := s.queries.GetUserByID(ctx, userID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, ErrUserNotFound
		}
		s.logger.Error("failed to get user by ID", "error", err, "user_id", userID)
		return nil, ErrInternalError
	}

	return s.mapUserToUserInfo(&user), nil
}

// Register creates a new user registration request
func (s *Service) Register(ctx context.Context, req RegistrationRequest) (*RegistrationResponse, error) {
	// Hash password
	hashedPassword, err := s.passwordService.HashPassword(req.Password)
	if err != nil {
		s.logger.Error("failed to hash password during registration", "error", err)
		return nil, ErrInternalError
	}

	// Convert optional fields to sql.Null types
	var address sql.NullString
	if req.Address != nil {
		address.String = *req.Address
		address.Valid = true
	}

	var jobTitle sql.NullString
	if req.JobTitle != nil {
		jobTitle.String = *req.JobTitle
		jobTitle.Valid = true
	}

	var phone sql.NullString
	if req.Phone != nil {
		phone.String = *req.Phone
		phone.Valid = true
	}

	var mobile sql.NullString
	if req.Mobile != nil {
		mobile.String = *req.Mobile
		mobile.Valid = true
	}

	var backupEmail sql.NullString
	if req.BackupEmail != nil {
		backupEmail.String = *req.BackupEmail
		backupEmail.Valid = true
	}

	var remark sql.NullString
	if req.Remark != nil {
		remark.String = *req.Remark
		remark.Valid = true
	}

	// Create company type
	var companyType sqlc.NullCompanyType
	if req.CompanyType != "" {
		companyType.CompanyType = sqlc.CompanyType(req.CompanyType)
		companyType.Valid = true
	}

	// Create registration request
	registration, err := s.queries.CreateRegistrationRequest(ctx, sqlc.CreateRegistrationRequestParams{
		UnifiedBusinessNo: req.UnifiedBusinessNo,
		CompanyName:       req.CompanyName,
		CompanyType:       companyType,
		Address:           address,
		CompanyOwner:      req.CompanyOwner,
		ContactPerson:     req.ContactPerson,
		JobTitle:          jobTitle,
		Phone:             phone,
		Mobile:            mobile,
		Email:             req.Email,
		BackupEmail:       backupEmail,
		PasswordHash:      hashedPassword,
		Status:            constants.RegistrationStatuses.PendingRegistration, // Default status for new registrations
		Remark:            remark,
	})

	if err != nil {
		s.logger.Error("failed to create registration request", "error", err, "email", req.Email)
		return nil, ErrInternalError
	}

	return &RegistrationResponse{
		ID:      registration.ID,
		Status:  registration.Status,
		Message: constants.MsgRegistered,
	}, nil
}

// validateUserStatus checks if user status allows login
func (s *Service) validateUserStatus(status sqlc.UserStatus) error {

	switch status {
	case sqlc.UserStatusValue0:
		return nil
	case sqlc.UserStatusValue1:
		return ErrAccountPendingChanges
	case sqlc.UserStatusValue2:
		return ErrAccountChangesRejected
	case sqlc.UserStatusValue3:
		return ErrAccountDeleted
	default:
		return ErrAccountPending
	}
}

// mapUserToUserInfo converts database user to UserInfo
func (s *Service) mapUserToUserInfo(user *sqlc.GetUserByIDRow) *UserInfo {
	var companyID *int32
	if user.CompanyID.Valid {
		companyID = &user.CompanyID.Int32
	}
	var companyName *string
	if user.CompanyName.Valid {
		companyName = &user.CompanyName.String
	}

	return &UserInfo{
		ID:                user.ID,
		Username:          user.Username,
		Email:             user.Email,
		Role:              user.UserRole,
		Status:            user.Status,
		CompanyID:         companyID,
		CompanyName:       companyName,
		LastLoginAt:       user.LastLoginAt,
		PasswordExpiredAt: user.PasswordExpirationAt,
	}
}

// Service-specific errors
var (
	ErrInvalidCredentials     = errors.New("invalid email or password")
	ErrPasswordExpired        = errors.New("password has expired")
	ErrAccountDeleted         = errors.New("account has been deleted")
	ErrAccountPending         = errors.New("account pending approval")
	ErrAccountPendingChanges  = errors.New("account changes pending approval")
	ErrAccountChangesRejected = errors.New("account changes rejected")
	ErrUserNotFound           = errors.New("user not found")
	ErrInternalError          = errors.New("internal server error")
)
