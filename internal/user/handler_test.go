// Package user_test 提供用戶處理器的單元測試
// 遵循 Go 測試最佳實踐，使用表格驅動測試和模擬依賴項
package user

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/koopa0/pms-api-v2/internal/auth"
	"github.com/koopa0/pms-api-v2/internal/constants"
	"github.com/koopa0/pms-api-v2/internal/testutils"
	"github.com/koopa0/pms-api-v2/internal/types"
	"github.com/koopa0/pms-api-v2/sqlc"
)

// MockUserService 用戶服務的模擬實現
// Following Architecture.md principle: test code defines interfaces based on what it needs
type MockUserService struct {
	getUserByIDFunc      func(ctx context.Context, userID int32) (*Info, error)
	updateUserFunc       func(ctx context.Context, userID int32, req UpdateUserRequest) (*Info, error)
	changePasswordFunc   func(ctx context.Context, userID int32, req ChangePasswordRequest) error
	listUsersFunc        func(ctx context.Context, req ListUsersRequest) (*ListUsersResponse, error)
	updateUserStatusFunc func(ctx context.Context, userID int32, status sqlc.UserStatus) (*Info, error)
}

func (m *MockUserService) GetUserByID(ctx context.Context, userID int32) (*Info, error) {
	if m.getUserByIDFunc != nil {
		return m.getUserByIDFunc(ctx, userID)
	}
	return nil, errors.New("mock not implemented")
}

func (m *MockUserService) UpdateUser(ctx context.Context, userID int32, req UpdateUserRequest) (*Info, error) {
	if m.updateUserFunc != nil {
		return m.updateUserFunc(ctx, userID, req)
	}
	return nil, errors.New("mock not implemented")
}

func (m *MockUserService) ChangePassword(ctx context.Context, userID int32, req ChangePasswordRequest) error {
	if m.changePasswordFunc != nil {
		return m.changePasswordFunc(ctx, userID, req)
	}
	return errors.New("mock not implemented")
}

func (m *MockUserService) ListUsers(ctx context.Context, req ListUsersRequest) (*ListUsersResponse, error) {
	if m.listUsersFunc != nil {
		return m.listUsersFunc(ctx, req)
	}
	return nil, errors.New("mock not implemented")
}

func (m *MockUserService) UpdateUserStatus(ctx context.Context, userID int32, status sqlc.UserStatus) (*Info, error) {
	if m.updateUserStatusFunc != nil {
		return m.updateUserStatusFunc(ctx, userID, status)
	}
	return nil, errors.New("mock not implemented")
}

// Following Architecture.md principle: test code defines interfaces based on what it needs
// UserServiceInterface defines the service operations needed by the handler tests
type UserServiceInterface interface {
	GetUserByID(ctx context.Context, userID int32) (*Info, error)
	UpdateUser(ctx context.Context, userID int32, req UpdateUserRequest) (*Info, error)
	ChangePassword(ctx context.Context, userID int32, req ChangePasswordRequest) error
	ListUsers(ctx context.Context, req ListUsersRequest) (*ListUsersResponse, error)
	UpdateUserStatus(ctx context.Context, userID int32, status sqlc.UserStatus) (*Info, error)
}

// TestUserService wraps the mock service to implement the Service interface
type TestUserService struct {
	service UserServiceInterface
}

func (t *TestUserService) GetUserByID(ctx context.Context, userID int32) (*Info, error) {
	return t.service.GetUserByID(ctx, userID)
}

func (t *TestUserService) UpdateUser(ctx context.Context, userID int32, req UpdateUserRequest) (*Info, error) {
	return t.service.UpdateUser(ctx, userID, req)
}

func (t *TestUserService) ChangePassword(ctx context.Context, userID int32, req ChangePasswordRequest) error {
	return t.service.ChangePassword(ctx, userID, req)
}

func (t *TestUserService) ListUsers(ctx context.Context, req ListUsersRequest) (*ListUsersResponse, error) {
	return t.service.ListUsers(ctx, req)
}

func (t *TestUserService) UpdateUserStatus(ctx context.Context, userID int32, status sqlc.UserStatus) (*Info, error) {
	return t.service.UpdateUserStatus(ctx, userID, status)
}

// setupHandler 設置測試用的處理器
func setupHandler(mockService *MockUserService) *Handler {
	testLogger := testutils.TestLogger()
	testService := &TestUserService{service: mockService}
	// Create a handler that accepts the concrete service type
	// For testing, we'll need to create a test-specific handler
	return &Handler{
		userService: (*Service)(testService), // This won't work directly
		logger:      testLogger,
	}
}

// Auth helper functions for user package tests
// Following Architecture.md principle: test code defines interfaces based on what it needs

// createAdminRequest creates an HTTP request with admin authentication
func createAdminRequest(method, url string, body interface{}) *http.Request {
	req := testutils.CreateRequest(method, url, body)
	claims := &auth.Claims{
		UserID:   1,
		Email:    "<EMAIL>",
		Username: "admin",
		Role:     constants.UserRoles.SPO,
	}
	ctx := context.WithValue(req.Context(), auth.ContextKeyUser, claims)
	return req.WithContext(ctx)
}

// createCompanyRequest creates an HTTP request with company authentication
func createCompanyRequest(method, url string, body interface{}, userID, companyID int32) *http.Request {
	req := testutils.CreateRequest(method, url, body)
	claims := &auth.Claims{
		UserID:    userID,
		Email:     "<EMAIL>",
		Username:  "company_user",
		Role:      constants.UserRoles.Company,
		CompanyID: &companyID,
	}
	ctx := context.WithValue(req.Context(), auth.ContextKeyUser, claims)
	return req.WithContext(ctx)
}

// createCISARequest creates an HTTP request with CISA authentication
func createCISARequest(method, url string, body interface{}, userID int32) *http.Request {
	req := testutils.CreateRequest(method, url, body)
	claims := &auth.Claims{
		UserID:   userID,
		Email:    "<EMAIL>",
		Username: "cisa_user",
		Role:     constants.UserRoles.CISA,
	}
	ctx := context.WithValue(req.Context(), auth.ContextKeyUser, claims)
	return req.WithContext(ctx)
}

// TestGetUserProfile 測試獲取當前用戶個人資料
func TestGetUserProfile(t *testing.T) {
	tests := []struct {
		name           string
		userID         int32
		mockSetup      func(*MockUserService)
		expectedStatus int
		validateFunc   func(t *testing.T, recorder *httptest.ResponseRecorder)
	}{
		{
			name:   "successful get profile",
			userID: 1,
			mockSetup: func(m *MockUserService) {
				m.getUserByIDFunc = func(ctx context.Context, userID int32) (*Info, error) {
					return &Info{
						ID:       userID,
						Username: "testuser",
						Email:    "<EMAIL>",
						Role:     constants.UserRoles.Company,
					}, nil
				}
			},
			expectedStatus: http.StatusOK,
			validateFunc: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				response := testutils.AssertSuccessResponse(t, recorder)
				if data, ok := response[constants.FieldNames.Data].(map[string]any); ok {
					if username, ok := data[constants.FieldNames.Username].(string); ok {
						if username != "testuser" {
							t.Errorf("Expected username 'testuser', got %q", username)
						}
					}
				}
			},
		},
		{
			name:           "unauthorized - no auth context",
			userID:         1,
			mockSetup:      func(m *MockUserService) {},
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:   "user not found",
			userID: 1,
			mockSetup: func(m *MockUserService) {
				m.getUserByIDFunc = func(ctx context.Context, userID int32) (*Info, error) {
					return nil, ErrUserNotFound
				}
			},
			expectedStatus: http.StatusNotFound,
		},
		{
			name:   "service error",
			userID: 1,
			mockSetup: func(m *MockUserService) {
				m.getUserByIDFunc = func(ctx context.Context, userID int32) (*Info, error) {
					return nil, errors.New("database error")
				}
			},
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService := &MockUserService{}
			tt.mockSetup(mockService)
			handler := setupHandler(mockService)

			var req *http.Request
			if tt.name == "unauthorized - no auth context" {
				req = testutils.CreateRequest("GET", "/api/v1/users/profile", nil)
			} else {
				req = createCompanyRequest("GET", "/api/v1/users/profile", nil, tt.userID, 100)
			}

			w := httptest.NewRecorder()
			handler.GetUserProfile(w, req)

			testutils.AssertStatus(t, w, tt.expectedStatus)

			if tt.validateFunc != nil {
				tt.validateFunc(t, w)
			}
		})
	}
}

// TestUpdateUserProfile 測試用戶個人資料更新
func TestUpdateUserProfile(t *testing.T) {
	tests := []struct {
		name           string
		userID         int32
		requestBody    string
		mockSetup      func(*MockUserService)
		expectedStatus int
		validateFunc   func(t *testing.T, recorder *httptest.ResponseRecorder)
	}{
		{
			name:   "successful update",
			userID: 1,
			requestBody: `{
				"username": "newuser",
				"email": "<EMAIL>"
			}`,
			mockSetup: func(m *MockUserService) {
				m.updateUserFunc = func(ctx context.Context, userID int32, req UpdateUserRequest) (*Info, error) {
					return &Info{
						ID:       userID,
						Username: req.Username,
						Email:    req.Email,
						Role:     constants.UserRoles.Company,
					}, nil
				}
			},
			expectedStatus: http.StatusOK,
			validateFunc: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				testutils.AssertSuccessResponse(t, recorder)
			},
		},
		{
			name:           "unauthorized - no auth context",
			userID:         1,
			requestBody:    `{"username": "test"}`,
			mockSetup:      func(m *MockUserService) {},
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "invalid JSON",
			userID:         1,
			requestBody:    `{"invalid": json}`,
			mockSetup:      func(m *MockUserService) {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:   "validation error - empty username",
			userID: 1,
			requestBody: `{
				"username": "",
				"email": "<EMAIL>"
			}`,
			mockSetup:      func(m *MockUserService) {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:   "validation error - invalid email",
			userID: 1,
			requestBody: `{
				"username": "testuser",
				"email": "invalid-email"
			}`,
			mockSetup:      func(m *MockUserService) {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:   "service error",
			userID: 1,
			requestBody: `{
				"username": "newuser",
				"email": "<EMAIL>"
			}`,
			mockSetup: func(m *MockUserService) {
				m.updateUserFunc = func(ctx context.Context, userID int32, req UpdateUserRequest) (*Info, error) {
					return nil, errors.New("service error")
				}
			},
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService := &MockUserService{}
			tt.mockSetup(mockService)
			handler := setupHandler(mockService)

			var req *http.Request
			if tt.name == "unauthorized - no auth context" {
				// Create a request without auth context
				req = testutils.CreateRequest(http.MethodPatch, "/api/v1/users/profile", tt.requestBody)
			} else {
				// Create an authenticated request
				req = createCompanyRequest(http.MethodPatch, "/api/v1/users/profile", tt.requestBody, tt.userID, 100)
			}

			w := httptest.NewRecorder()
			handler.UpdateUserProfile(w, req)

			testutils.AssertStatus(t, w, tt.expectedStatus)

			if tt.validateFunc != nil {
				tt.validateFunc(t, w)
			}
		})
	}
}

// TestChangePassword 測試密碼變更
func TestChangePassword(t *testing.T) {
	tests := []struct {
		name           string
		userID         int32
		requestBody    string
		mockSetup      func(*MockUserService)
		expectedStatus int
		validateFunc   func(t *testing.T, recorder *httptest.ResponseRecorder)
	}{
		{
			name:   "successful password change",
			userID: 1,
			requestBody: `{
				"old_password": "oldpass123",
				"new_password": "newpass123"
			}`,
			mockSetup: func(m *MockUserService) {
				m.changePasswordFunc = func(ctx context.Context, userID int32, req ChangePasswordRequest) error {
					return nil
				}
			},
			expectedStatus: http.StatusOK,
			validateFunc: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				response := testutils.AssertSuccessResponse(t, recorder)
				if data, ok := response[constants.FieldNames.Data].(map[string]any); ok {
					if message, ok := data[constants.FieldNames.Message].(string); ok {
						if message != constants.MsgPasswordChanged {
							t.Errorf("Expected message %q, got %q", constants.MsgPasswordChanged, message)
						}
					}
				}
			},
		},
		{
			name:           "unauthorized",
			userID:         1,
			requestBody:    `{"old_password": "old", "new_password": "new"}`,
			mockSetup:      func(m *MockUserService) {},
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "invalid JSON",
			userID:         1,
			requestBody:    `{"invalid": json}`,
			mockSetup:      func(m *MockUserService) {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:   "validation error - short password",
			userID: 1,
			requestBody: `{
				"old_password": "old123",
				"new_password": "short"
			}`,
			mockSetup:      func(m *MockUserService) {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:   "service error - wrong old password",
			userID: 1,
			requestBody: `{
				"old_password": "wrongpassword",
				"new_password": "newpass123"
			}`,
			mockSetup: func(m *MockUserService) {
				m.changePasswordFunc = func(ctx context.Context, userID int32, req ChangePasswordRequest) error {
					return ErrInvalidPassword
				}
			},
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService := &MockUserService{}
			tt.mockSetup(mockService)
			handler := setupHandler(mockService)

			var req *http.Request
			if tt.name == "unauthorized" {
				req = testutils.CreateRequest(http.MethodPost, "/api/v1/users/change-password", tt.requestBody)
			} else {
				req = createCompanyRequest(http.MethodPost, "/api/v1/users/change-password", tt.requestBody, tt.userID, 100)
			}

			w := httptest.NewRecorder()
			handler.ChangePassword(w, req)

			testutils.AssertStatus(t, w, tt.expectedStatus)

			if tt.validateFunc != nil {
				tt.validateFunc(t, w)
			}
		})
	}
}

// TestListUsers 測試用戶列表查詢（管理員功能）
func TestListUsers(t *testing.T) {
	tests := []struct {
		name           string
		userRole       sqlc.UserRole
		queryParams    string
		mockSetup      func(*MockUserService)
		expectedStatus int
		validateFunc   func(t *testing.T, recorder *httptest.ResponseRecorder)
	}{
		{
			name:     "successful list as admin",
			userRole: constants.UserRoles.SPO,
			mockSetup: func(m *MockUserService) {
				m.listUsersFunc = func(ctx context.Context, req ListUsersRequest) (*ListUsersResponse, error) {
					users := []Info{
						{
							ID:       1,
							Username: "user1",
							Email:    "<EMAIL>",
							Role:     constants.UserRoles.Company,
						},
					}
					return &ListUsersResponse{
						Data: users,
						Pagination: types.PaginationMeta{
							Page:        1,
							PageSize:    20,
							Total:       1,
							TotalPages:  1,
							HasNext:     false,
							HasPrevious: false,
						},
						Success: true,
					}, nil
				}
			},
			expectedStatus: http.StatusOK,
			validateFunc: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				response := testutils.AssertSuccessResponse(t, recorder)
				if data, ok := response["data"].(map[string]any); ok {
					if users, ok := data["data"].([]any); ok {
						if len(users) != 1 {
							t.Errorf("Expected 1 user, got %d", len(users))
						}
					}
				}
			},
		},
		{
			name:        "successful list with pagination",
			userRole:    constants.UserRoles.SPO,
			queryParams: "?page=2&page_size=10",
			mockSetup: func(m *MockUserService) {
				m.listUsersFunc = func(ctx context.Context, req ListUsersRequest) (*ListUsersResponse, error) {
					if req.Pagination.Page != 2 || req.Pagination.PageSize != 10 {
						return nil, errors.New("pagination not passed correctly")
					}
					return &ListUsersResponse{
						Data: []Info{},
						Pagination: types.PaginationMeta{
							Page:        2,
							PageSize:    10,
							Total:       0,
							TotalPages:  0,
							HasNext:     false,
							HasPrevious: true,
						},
						Success: true,
					}, nil
				}
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:        "successful list with filters",
			userRole:    constants.UserRoles.SPO,
			queryParams: "?role=Company&status=通過",
			mockSetup: func(m *MockUserService) {
				m.listUsersFunc = func(ctx context.Context, req ListUsersRequest) (*ListUsersResponse, error) {
					if req.Role != constants.UserRoles.Company || req.Status != "通過" {
						return nil, errors.New("filters not passed correctly")
					}
					return &ListUsersResponse{
						Data: []Info{},
						Pagination: types.PaginationMeta{
							Page:        1,
							PageSize:    20,
							Total:       0,
							TotalPages:  0,
							HasNext:     false,
							HasPrevious: false,
						},
						Success: true,
					}, nil
				}
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:           "forbidden for non-admin",
			userRole:       constants.UserRoles.Company,
			mockSetup:      func(m *MockUserService) {},
			expectedStatus: http.StatusForbidden,
		},
		{
			name:           "unauthorized",
			userRole:       constants.UserRoles.Company,
			mockSetup:      func(m *MockUserService) {},
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:     "service error",
			userRole: constants.UserRoles.SPO,
			mockSetup: func(m *MockUserService) {
				m.listUsersFunc = func(ctx context.Context, req ListUsersRequest) (*ListUsersResponse, error) {
					return nil, errors.New("database error")
				}
			},
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService := &MockUserService{}
			tt.mockSetup(mockService)
			handler := setupHandler(mockService)

			url := "/api/v1/users" + tt.queryParams
			var req *http.Request

			if tt.name == "unauthorized" {
				req = testutils.CreateRequest("GET", url, nil)
			} else if tt.userRole == constants.UserRoles.SPO {
				req = createAdminRequest("GET", url, nil)
			} else {
				req = createCompanyRequest("GET", url, nil, 1, 100)
			}

			w := httptest.NewRecorder()
			handler.ListUsers(w, req)

			testutils.AssertStatus(t, w, tt.expectedStatus)

			if tt.validateFunc != nil {
				tt.validateFunc(t, w)
			}
		})
	}
}

// TestGetUserByID 測試通過 ID 獲取用戶信息
func TestGetUserByID(t *testing.T) {
	tests := []struct {
		name           string
		userID         string
		mockSetup      func(*MockUserService)
		expectedStatus int
		validateFunc   func(t *testing.T, recorder *httptest.ResponseRecorder)
	}{
		{
			name:   "successful get user",
			userID: "123",
			mockSetup: func(m *MockUserService) {
				m.getUserByIDFunc = func(ctx context.Context, userID int32) (*Info, error) {
					return &Info{
						ID:       userID,
						Username: "testuser",
						Email:    "<EMAIL>",
						Role:     constants.UserRoles.Company,
					}, nil
				}
			},
			expectedStatus: http.StatusOK,
			validateFunc: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				response := testutils.AssertSuccessResponse(t, recorder)
				if data, ok := response[constants.FieldNames.Data].(map[string]any); ok {
					if username, ok := data[constants.FieldNames.Username].(string); ok {
						if username != "testuser" {
							t.Errorf("Expected username 'testuser', got %q", username)
						}
					}
				}
			},
		},
		{
			name:           "unauthorized",
			userID:         "123",
			mockSetup:      func(m *MockUserService) {},
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "invalid user ID",
			userID:         "invalid",
			mockSetup:      func(m *MockUserService) {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:   "user not found",
			userID: "999",
			mockSetup: func(m *MockUserService) {
				m.getUserByIDFunc = func(ctx context.Context, userID int32) (*Info, error) {
					return nil, ErrUserNotFound
				}
			},
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService := &MockUserService{}
			tt.mockSetup(mockService)
			handler := setupHandler(mockService)

			url := "/api/v1/users/" + tt.userID
			var req *http.Request

			if tt.name == "unauthorized" {
				req = testutils.CreateRequestWithPathValues("GET", url, nil, map[string]string{"id": tt.userID})
			} else {
				req = createAdminRequest("GET", url, nil)
				req.SetPathValue("id", tt.userID)
			}

			w := httptest.NewRecorder()
			handler.GetUserByID(w, req)

			testutils.AssertStatus(t, w, tt.expectedStatus)

			if tt.validateFunc != nil {
				tt.validateFunc(t, w)
			}
		})
	}
}

// TestUpdateUserStatus 測試更新用戶狀態
func TestUpdateUserStatus(t *testing.T) {
	tests := []struct {
		name           string
		userID         string
		requestBody    string
		mockSetup      func(*MockUserService)
		expectedStatus int
		validateFunc   func(t *testing.T, recorder *httptest.ResponseRecorder)
	}{
		{
			name:   "successful status update",
			userID: "123",
			requestBody: `{
				"status": "通過"
			}`,
			mockSetup: func(m *MockUserService) {
				m.updateUserStatusFunc = func(ctx context.Context, userID int32, status sqlc.UserStatus) (*Info, error) {
					return &Info{
						ID:       userID,
						Username: "testuser",
						Email:    "<EMAIL>",
						Role:     constants.UserRoles.Company,
						Status:   status,
					}, nil
				}
			},
			expectedStatus: http.StatusOK,
			validateFunc: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				testutils.AssertSuccessResponse(t, recorder)
			},
		},
		{
			name:           "unauthorized",
			userID:         "123",
			requestBody:    `{"status": "通過"}`,
			mockSetup:      func(m *MockUserService) {},
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "invalid user ID",
			userID:         "invalid",
			requestBody:    `{"status": "通過"}`,
			mockSetup:      func(m *MockUserService) {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:   "invalid status",
			userID: "123",
			requestBody: `{
				"status": "無效狀態"
			}`,
			mockSetup:      func(m *MockUserService) {},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:   "service error",
			userID: "123",
			requestBody: `{
				"status": "通過"
			}`,
			mockSetup: func(m *MockUserService) {
				m.updateUserStatusFunc = func(ctx context.Context, userID int32, status sqlc.UserStatus) (*Info, error) {
					return nil, errors.New("database error")
				}
			},
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockService := &MockUserService{}
			tt.mockSetup(mockService)
			handler := setupHandler(mockService)

			url := "/api/v1/users/" + tt.userID + "/status"
			var req *http.Request

			if tt.name == "unauthorized" {
				req = testutils.CreateRequestWithPathValues("PATCH", url, tt.requestBody, map[string]string{"id": tt.userID})
			} else {
				req = createAdminRequest("PATCH", url, tt.requestBody)
				req.SetPathValue("id", tt.userID)
			}

			w := httptest.NewRecorder()
			handler.UpdateUserStatus(w, req)

			testutils.AssertStatus(t, w, tt.expectedStatus)

			if tt.validateFunc != nil {
				tt.validateFunc(t, w)
			}
		})
	}
}

// TestValidateUpdateUserRequest 測試用戶更新請求驗證
func TestValidateUpdateUserRequest(t *testing.T) {
	handler := setupHandler(&MockUserService{})

	tests := []struct {
		name    string
		request UpdateUserRequest
		wantErr bool
	}{
		{
			name: "valid request",
			request: UpdateUserRequest{
				Username: "validuser",
				Email:    "<EMAIL>",
			},
			wantErr: false,
		},
		{
			name: "empty username",
			request: UpdateUserRequest{
				Username: "",
				Email:    "<EMAIL>",
			},
			wantErr: true,
		},
		{
			name: "invalid email",
			request: UpdateUserRequest{
				Username: "validuser",
				Email:    "invalid-email",
			},
			wantErr: true,
		},
		{
			name: "username too short",
			request: UpdateUserRequest{
				Username: "ab",
				Email:    "<EMAIL>",
			},
			wantErr: true,
		},
		{
			name: "username too long",
			request: UpdateUserRequest{
				Username: "verylongusernamethatexceedsthemaximumlengthallowedfortheusername",
				Email:    "<EMAIL>",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := handler.validateUpdateUserRequest(&tt.request); (err != nil) != tt.wantErr {
				t.Errorf("validateUpdateUserRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestValidateChangePasswordRequest 測試密碼變更請求驗證
func TestValidateChangePasswordRequest(t *testing.T) {
	handler := setupHandler(&MockUserService{})

	tests := []struct {
		name    string
		request ChangePasswordRequest
		wantErr bool
	}{
		{
			name: "valid request",
			request: ChangePasswordRequest{
				OldPassword: "oldpass123",
				NewPassword: "newpass123",
			},
			wantErr: false,
		},
		{
			name: "empty old password",
			request: ChangePasswordRequest{
				OldPassword: "",
				NewPassword: "newpass123",
			},
			wantErr: true,
		},
		{
			name: "new password too short",
			request: ChangePasswordRequest{
				OldPassword: "oldpass123",
				NewPassword: "short",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := handler.validateChangePasswordRequest(&tt.request); (err != nil) != tt.wantErr {
				t.Errorf("validateChangePasswordRequest() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestValidateUserStatus 測試用戶狀態驗證
func TestValidateUserStatus(t *testing.T) {
	handler := setupHandler(&MockUserService{})

	tests := []struct {
		name    string
		status  sqlc.UserStatus
		wantErr bool
	}{
		{
			name:    "valid status - approved",
			status:  constants.UserStatuses.Approved,
			wantErr: false,
		},
		{
			name:    "valid status - pending change",
			status:  constants.UserStatuses.PendingChange,
			wantErr: false,
		},
		{
			name:    "invalid status",
			status:  sqlc.UserStatus("invalid_status"),
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := handler.validateUserStatus(tt.status); (err != nil) != tt.wantErr {
				t.Errorf("validateUserStatus() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// TestHandleServiceError 測試服務錯誤處理
func TestHandleServiceError(t *testing.T) {
	handler := setupHandler(&MockUserService{})

	tests := []struct {
		name           string
		err            error
		expectedStatus int
		expectedMsg    string
	}{
		{
			name:           "user not found error",
			err:            ErrUserNotFound,
			expectedStatus: http.StatusNotFound,
			expectedMsg:    "找不到資源", // Updated to match common API error handler
		},
		{
			name:           "invalid password error",
			err:            ErrInvalidPassword,
			expectedStatus: http.StatusBadRequest,
			expectedMsg:    "bad request: invalid password", // Updated to match wrapped error format
		},
		{
			name:           "weak password error",
			err:            ErrWeakPassword,
			expectedStatus: http.StatusBadRequest,
			expectedMsg:    "bad request: password does not meet strength requirements", // Updated to match wrapped error format
		},
		{
			name:           "internal error",
			err:            ErrInternalError,
			expectedStatus: http.StatusInternalServerError,
			expectedMsg:    "內部伺服器錯誤",
		},
		{
			name:           "unknown error",
			err:            errors.New("unknown error"),
			expectedStatus: http.StatusInternalServerError,
			expectedMsg:    "內部伺服器錯誤",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			handler.handleServiceError(w, tt.err)

			testutils.AssertStatus(t, w, tt.expectedStatus)
			testutils.AssertErrorResponse(t, w, tt.expectedMsg)
		})
	}
}

// TestAdminRoleRequirement 測試管理員權限檢查 (通過實際的handler方法)
func TestAdminRoleRequirement(t *testing.T) {
	mockService := &MockUserService{
		listUsersFunc: func(ctx context.Context, req ListUsersRequest) (*ListUsersResponse, error) {
			// Return a simple successful response for admin users
			return &ListUsersResponse{
				Data: []Info{},
				Pagination: types.PaginationMeta{
					Page:        1,
					PageSize:    20,
					Total:       0,
					TotalPages:  0,
					HasNext:     false,
					HasPrevious: false,
				},
				Success: true,
			}, nil
		},
	}
	handler := setupHandler(mockService)

	tests := []struct {
		name           string
		userRole       sqlc.UserRole
		expectedStatus int
	}{
		{
			name:           "SPO admin role - should succeed",
			userRole:       constants.UserRoles.SPO,
			expectedStatus: http.StatusOK,
		},
		{
			name:           "CISA role - should be forbidden",
			userRole:       constants.UserRoles.CISA,
			expectedStatus: http.StatusForbidden,
		},
		{
			name:           "Company role - should be forbidden",
			userRole:       constants.UserRoles.Company,
			expectedStatus: http.StatusForbidden,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var req *http.Request
			if tt.userRole == constants.UserRoles.SPO {
				req = createAdminRequest("GET", "/api/v1/users", nil)
			} else if tt.userRole == constants.UserRoles.CISA {
				req = createCISARequest("GET", "/api/v1/users", nil, 1)
			} else {
				req = createCompanyRequest("GET", "/api/v1/users", nil, 1, 100)
			}

			w := httptest.NewRecorder()
			handler.ListUsers(w, req)

			testutils.AssertStatus(t, w, tt.expectedStatus)
		})
	}
}

// TestValidationEdgeCases 測試驗證邊界情況
func TestValidationEdgeCases(t *testing.T) {
	handler := setupHandler(&MockUserService{})

	t.Run("validate update user request with backup email", func(t *testing.T) {
		backupEmail := "<EMAIL>"
		req := &UpdateUserRequest{
			Username:    "validuser",
			Email:       "<EMAIL>",
			BackupEmail: &backupEmail,
		}

		if err := handler.validateUpdateUserRequest(req); err != nil {
			t.Errorf("Expected no error for valid backup email, got: %v", err)
		}
	})

	t.Run("validate update user request with invalid backup email", func(t *testing.T) {
		invalidBackupEmail := "invalid-email"
		req := &UpdateUserRequest{
			Username:    "validuser",
			Email:       "<EMAIL>",
			BackupEmail: &invalidBackupEmail,
		}

		err := handler.validateUpdateUserRequest(req)
		if err == nil {
			t.Error("Expected error for invalid backup email")
		}
	})

	t.Run("validate update user request with job title", func(t *testing.T) {
		jobTitle := "Software Engineer"
		req := &UpdateUserRequest{
			Username: "validuser",
			Email:    "<EMAIL>",
			JobTitle: &jobTitle,
		}

		if err := handler.validateUpdateUserRequest(req); err != nil {
			t.Errorf("Expected no error for valid job title, got: %v", err)
		}
	})

	t.Run("validate update user request with too long job title", func(t *testing.T) {
		longJobTitle := "Very Long Job Title That Exceeds The Maximum Length Allowed For Job Titles In The System And Should Cause Validation Error"
		req := &UpdateUserRequest{
			Username: "validuser",
			Email:    "<EMAIL>",
			JobTitle: &longJobTitle,
		}

		if err := handler.validateUpdateUserRequest(req); err == nil {
			t.Error("Expected error for too long job title")
		}
	})

	t.Run("validate update user request with mobile", func(t *testing.T) {
		mobile := "0912345678"
		req := &UpdateUserRequest{
			Username: "validuser",
			Email:    "<EMAIL>",
			Mobile:   &mobile,
		}

		if err := handler.validateUpdateUserRequest(req); err != nil {
			t.Errorf("Expected no error for valid mobile, got: %v", err)
		}
	})

	t.Run("validate update user request with too long mobile", func(t *testing.T) {
		longMobile := "012345678901234567890123456789"
		req := &UpdateUserRequest{
			Username: "validuser",
			Email:    "<EMAIL>",
			Mobile:   &longMobile,
		}

		if err := handler.validateUpdateUserRequest(req); err == nil {
			t.Error("Expected error for too long mobile")
		}
	})
}
