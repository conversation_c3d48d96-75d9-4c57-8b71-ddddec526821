// Package config 提供應用程式配置管理功能
// 負責從環境變數載入設定，支援開發、測試、生產環境的不同配置
// Package config 提供應用程式配置管理功能
// 負責從環境變數載入設定，支援開發、測試、生產環境的不同配置
package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/joho/godotenv"
)

// 預設配置常數 - 避免在程式碼中使用魔術數字
const (
	// 資料庫連線池預設值
	DefaultMaxConnections     = 25
	DefaultMaxIdleConnections = 5
	DefaultMaxLifetime        = "5m"

	// HTTP 伺服器預設值
	DefaultServerPort   = "8080"
	DefaultReadTimeout  = "15s"
	DefaultWriteTimeout = "15s"
	DefaultIdleTimeout  = "60s"

	// JWT 預設值
	DefaultIssuer             = "pms-api"
	DefaultAccessTokenExpiry  = "24h"
	DefaultRefreshTokenExpiry = "168h" // 7 天

	// 檔案上傳預設值
	DefaultMaxUploadSize = 10 * 1024 * 1024 // 10MB

	// SMTP 預設值
	DefaultSMTPHost = "smtp.gmail.com"
	DefaultSMTPPort = 587

	// 環境和日誌預設值
	DefaultEnvironment = "development"
	DefaultLogLevel    = "info"

	// 生產環境標識
	EnvironmentProduction  = "production"
	EnvironmentDevelopment = "development"
)

// Config 是應用程式的主要設定結構
// 包含所有子系統的配置設定
type Config struct {
	Database   DatabaseConfig
	Server     ServerConfig
	JWT        JWTConfig
	CORS       CORSConfig
	FileUpload FileUploadConfig
	Email      EmailConfig
	Env        string
	LogLevel   string
}

// DatabaseConfig 資料庫連線設定
// 包含 PostgreSQL 連線池相關參數
type DatabaseConfig struct {
	URL            string
	MaxConnections int32
	MaxIdleConns   int32
	MaxLifetime    time.Duration
}

// ServerConfig HTTP 伺服器設定
// 包含連接埠、逾時等伺服器參數
type ServerConfig struct {
	Port         string
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	IdleTimeout  time.Duration
}

// JWTConfig JWT 認證設定
// 包含簽名金鑰、發行者、Token 有效期限等參數
type JWTConfig struct {
	SecretKey          string
	Issuer             string
	AccessTokenExpiry  time.Duration
	RefreshTokenExpiry time.Duration
}

// CORSConfig 跨域資源共享（CORS）設定
// 控制哪些網域可以存取 API
type CORSConfig struct {
	AllowedOrigins []string
	AllowedMethods []string
	AllowedHeaders []string
}

// FileUploadConfig 檔案上傳設定
// 包含檔案大小限制和允許的檔案類型
type FileUploadConfig struct {
	MaxUploadSize    int64
	AllowedFileTypes []string
}

// EmailConfig 電子郵件伺服器設定
// 用於發送系統通知和稽催郵件
type EmailConfig struct {
	Host     string
	Port     int
	Username string
	Password string
	From     string
}

// Load 從環境變數載入應用程式設定
// 優先使用環境變數，若無則使用預設值
// 開發環境會嘗試載入 .env 檔案
func Load() (*Config, error) {
	// 在開發環境載入 .env 檔案（生產環境不需要）
	if err := godotenv.Load(); err != nil {
		// .env 檔案在生產環境不存在是正常的
		if !os.IsNotExist(err) {
			return nil, fmt.Errorf("error loading .env file: %w", err)
		}
	}

	cfg := &Config{}

	// 資料庫設定
	maxConnections := getEnvAsInt("DATABASE_MAX_CONNECTIONS", DefaultMaxConnections)
	maxIdleConns := getEnvAsInt("DATABASE_MAX_IDLE_CONNECTIONS", DefaultMaxIdleConnections)

	// Validate database connection limits to prevent overflow
	if maxConnections > 1000 {
		maxConnections = 1000
	}
	if maxConnections < 1 {
		maxConnections = DefaultMaxConnections
	}
	if maxIdleConns > maxConnections {
		maxIdleConns = maxConnections
	}
	if maxIdleConns < 1 {
		maxIdleConns = DefaultMaxIdleConnections
	}

	cfg.Database = DatabaseConfig{
		URL:            getEnv("DATABASE_URL", "postgres://postgres:password@localhost:5432/pms_db?sslmode=disable"),
		MaxConnections: int32(maxConnections),
		MaxIdleConns:   int32(maxIdleConns),
		MaxLifetime:    getEnvAsDuration("DATABASE_MAX_LIFETIME", DefaultMaxLifetime),
	}

	// HTTP 伺服器設定
	cfg.Server = ServerConfig{
		Port:         getEnv("SERVER_PORT", DefaultServerPort),
		ReadTimeout:  getEnvAsDuration("SERVER_READ_TIMEOUT", DefaultReadTimeout),
		WriteTimeout: getEnvAsDuration("SERVER_WRITE_TIMEOUT", DefaultWriteTimeout),
		IdleTimeout:  getEnvAsDuration("SERVER_IDLE_TIMEOUT", DefaultIdleTimeout),
	}

	// JWT 認證設定
	cfg.JWT = JWTConfig{
		SecretKey:          getEnv("JWT_SECRET_KEY", ""),
		Issuer:             getEnv("JWT_ISSUER", DefaultIssuer),
		AccessTokenExpiry:  getEnvAsDuration("JWT_ACCESS_TOKEN_EXPIRY", DefaultAccessTokenExpiry),
		RefreshTokenExpiry: getEnvAsDuration("JWT_REFRESH_TOKEN_EXPIRY", DefaultRefreshTokenExpiry),
	}

	// 驗證 JWT 簽名金鑰是否設定（生產環境必須）
	if cfg.JWT.SecretKey == "" {
		return nil, fmt.Errorf("JWT_SECRET_KEY is required")
	}

	// CORS 跨域設定
	cfg.CORS = CORSConfig{
		AllowedOrigins: getEnvAsSlice("CORS_ALLOWED_ORIGINS", []string{"http://localhost:3000"}),
		AllowedMethods: getEnvAsSlice("CORS_ALLOWED_METHODS", []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}),
		AllowedHeaders: getEnvAsSlice("CORS_ALLOWED_HEADERS", []string{"Content-Type", "Authorization"}),
	}

	// 檔案上傳設定
	cfg.FileUpload = FileUploadConfig{
		MaxUploadSize:    getEnvAsInt64("MAX_UPLOAD_SIZE", DefaultMaxUploadSize),
		AllowedFileTypes: getEnvAsSlice("ALLOWED_FILE_TYPES", []string{".pdf", ".doc", ".docx", ".xls", ".xlsx", ".png", ".jpg", ".jpeg"}),
	}

	// 電子郵件設定
	cfg.Email = EmailConfig{
		Host:     getEnv("SMTP_HOST", DefaultSMTPHost),
		Port:     getEnvAsInt("SMTP_PORT", DefaultSMTPPort),
		Username: getEnv("SMTP_USERNAME", ""),
		Password: getEnv("SMTP_PASSWORD", ""),
		From:     getEnv("SMTP_FROM", "<EMAIL>"),
	}

	// 環境和日誌設定
	cfg.Env = getEnv("ENVIRONMENT", DefaultEnvironment)
	cfg.LogLevel = getEnv("LOG_LEVEL", DefaultLogLevel)

	return cfg, nil
}

// IsProduction 檢查是否為生產環境
// 用於啟用生產環境特定的安全性檢查和最佳化
func (c *Config) IsProduction() bool {
	return c.Env == EnvironmentProduction
}

// IsDevelopment 檢查是否為開發環境
// 用於啟用開發環境的除錯功能
func (c *Config) IsDevelopment() bool {
	return c.Env == EnvironmentDevelopment
}

// 輔助函數

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intVal, err := strconv.Atoi(value); err == nil {
			return intVal
		}
	}
	return defaultValue
}

func getEnvAsInt64(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if intVal, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intVal
		}
	}
	return defaultValue
}

func getEnvAsDuration(key string, defaultValue string) time.Duration {
	value := getEnv(key, defaultValue)
	if duration, err := time.ParseDuration(value); err == nil {
		return duration
	}
	// 解析失敗時返回預設值
	duration, _ := time.ParseDuration(defaultValue)
	return duration
}

func getEnvAsSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		// 簡單的逗號分隔解析
		var result []string
		parts := strings.Split(value, ",")
		for _, v := range parts {
			v = strings.TrimSpace(v)
			if v != "" {
				result = append(result, v)
			}
		}
		return result
	}
	return defaultValue
}
