package sqlc

import (
	"context"
	"database/sql"
	"testing"
)

// Following Architecture.md principle: test code defines interfaces based on what it needs
// SQLC package provides generated database code, tests verify interface behavior

// MockDBTX implements DBTX interface for testing
type MockDBTX struct {
	shouldFailExec     bool
	shouldFailPrepare  bool
	shouldFailQuery    bool
	shouldFailQueryRow bool
	execError          error
	prepareError       error
	queryError         error
	queryRowError      error
	execResult         sql.Result
	preparedStmt       *sql.Stmt
	queryRows          *sql.Rows
	queryRow           *sql.Row
}

func NewMockDBTX() *MockDBTX {
	return &MockDBTX{}
}

func (m *MockDBTX) ExecContext(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	if m.shouldFailExec {
		return nil, m.execError
	}
	return m.execResult, nil
}

func (m *MockDBTX) PrepareContext(ctx context.Context, query string) (*sql.Stmt, error) {
	if m.shouldFailPrepare {
		return nil, m.prepareError
	}
	return m.preparedStmt, nil
}

func (m *MockDBTX) QueryContext(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	if m.shouldFailQuery {
		return nil, m.queryError
	}
	return m.queryRows, nil
}

func (m *MockDBTX) QueryRowContext(ctx context.Context, query string, args ...interface{}) *sql.Row {
	if m.shouldFailQueryRow {
		// For QueryRowContext, we can't return an error directly
		// The error would be returned when scanning the row
		return nil
	}
	return m.queryRow
}

func TestNew(t *testing.T) {
	t.Parallel()

	mockDB := NewMockDBTX()
	queries := New(mockDB)

	if queries == nil {
		t.Error("expected queries to be created")
	}

	if queries.db != mockDB {
		t.Error("expected db to be set correctly")
	}
}

func TestQueries_WithTx(t *testing.T) {
	t.Parallel()

	// Create initial queries with mock DB
	mockDB := NewMockDBTX()
	queries := New(mockDB)

	// Create a mock transaction (nil for testing)
	var mockTx *sql.Tx

	// Test WithTx
	txQueries := queries.WithTx(mockTx)

	if txQueries == nil {
		t.Error("expected transaction queries to be created")
	}

	if txQueries.db != mockTx {
		t.Error("expected transaction queries to use transaction as db")
	}

	// Verify original queries are unchanged
	if queries.db != mockDB {
		t.Error("expected original queries to remain unchanged")
	}
}

func TestDBTXInterface(t *testing.T) {
	t.Parallel()

	// Test that our mock implements DBTX interface
	var _ DBTX = (*MockDBTX)(nil)

	// Test that sql.DB implements DBTX interface (compile-time check)
	var _ DBTX = (*sql.DB)(nil)

	// Test that sql.Tx implements DBTX interface (compile-time check)
	var _ DBTX = (*sql.Tx)(nil)
}

func TestMockDBTX_ExecContext(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		setupMock   func(*MockDBTX)
		expectError bool
	}{
		{
			name: "successful execution",
			setupMock: func(m *MockDBTX) {
				// Default mock setup - no error
			},
			expectError: false,
		},
		{
			name: "execution error",
			setupMock: func(m *MockDBTX) {
				m.shouldFailExec = true
				m.execError = sql.ErrConnDone
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB := NewMockDBTX()
			tt.setupMock(mockDB)

			ctx := context.Background()
			_, err := mockDB.ExecContext(ctx, "SELECT 1", nil)

			if tt.expectError {
				if err == nil {
					t.Error("expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

func TestMockDBTX_PrepareContext(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		setupMock   func(*MockDBTX)
		expectError bool
	}{
		{
			name: "successful prepare",
			setupMock: func(m *MockDBTX) {
				// Default mock setup - no error
			},
			expectError: false,
		},
		{
			name: "prepare error",
			setupMock: func(m *MockDBTX) {
				m.shouldFailPrepare = true
				m.prepareError = sql.ErrConnDone
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB := NewMockDBTX()
			tt.setupMock(mockDB)

			ctx := context.Background()
			_, err := mockDB.PrepareContext(ctx, "SELECT 1")

			if tt.expectError {
				if err == nil {
					t.Error("expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

func TestMockDBTX_QueryContext(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		setupMock   func(*MockDBTX)
		expectError bool
	}{
		{
			name: "successful query",
			setupMock: func(m *MockDBTX) {
				// Default mock setup - no error
			},
			expectError: false,
		},
		{
			name: "query error",
			setupMock: func(m *MockDBTX) {
				m.shouldFailQuery = true
				m.queryError = sql.ErrConnDone
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB := NewMockDBTX()
			tt.setupMock(mockDB)

			ctx := context.Background()
			_, err := mockDB.QueryContext(ctx, "SELECT 1", nil)

			if tt.expectError {
				if err == nil {
					t.Error("expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

func TestMockDBTX_QueryRowContext(t *testing.T) {
	t.Parallel()

	mockDB := NewMockDBTX()
	ctx := context.Background()

	// Test normal operation
	row := mockDB.QueryRowContext(ctx, "SELECT 1", nil)
	if row != nil && mockDB.shouldFailQueryRow {
		t.Error("expected nil row when shouldFailQueryRow is true")
	}

	// Test with failure flag
	mockDB.shouldFailQueryRow = true
	row = mockDB.QueryRowContext(ctx, "SELECT 1", nil)
	if row != nil {
		t.Error("expected nil row when shouldFailQueryRow is true")
	}
}

func TestQuerierInterface(t *testing.T) {
	t.Parallel()

	// Test that Queries implements Querier interface (compile-time check)
	var _ Querier = (*Queries)(nil)

	// Verify the interface assertion in the generated code
	mockDB := NewMockDBTX()
	queries := New(mockDB)

	// This should not panic if the interface is properly implemented
	var querier Querier = queries
	if querier == nil {
		t.Error("expected querier to be non-nil")
	}
}
